========================================
AIStudio Real-time Log: main
Started: 2025-08-24T23:46:09.108Z
File: app_main_2025-08-24_18-46-09_001.log
========================================

[2025-08-24T23:46:09.342Z] [INFO] AIStudio application started successfully
[2025-08-24T23:46:09.342Z] [INFO] [main] AIStudio application started successfully
[2025-08-24T23:46:09.374Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-24T23:46:10.327Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-24T23:46:19.939Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-24_18-46-19_001.log
[2025-08-24T23:46:19.939Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-24T23:46:22.581Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-24T23:46:22.582Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-24T23:46:23.585Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-24T23:50:30.489Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_python installation_2025-08-24_18-50-30_001.log
[2025-08-24T23:50:30.489Z] [INFO] [dependency_core] DependencyManager: Starting python installation for Core
[2025-08-24T23:50:30.490Z] [INFO] [dependency_core] DependencyManager: Installing dependencies for Core (python:all)
[2025-08-24T23:50:30.491Z] [INFO] [dependency_core] DependencyManager: Component type: string, Component value: 'python'
[2025-08-24T23:50:30.491Z] [INFO] [dependency_core] DependencyManager: Name type: string, Name value: 'all'
[2025-08-24T23:50:30.491Z] [INFO] [dependency_core] DependencyManager: About to check routing for Core with component python
