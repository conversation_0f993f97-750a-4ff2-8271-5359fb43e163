========================================
AIStudio Real-time Log: main
Started: 2025-08-24T23:33:21.168Z
File: app_main_2025-08-24_18-33-21_001.log
========================================

[2025-08-24T23:33:21.395Z] [INFO] AIStudio application started successfully
[2025-08-24T23:33:21.395Z] [INFO] [main] AIStudio application started successfully
[2025-08-24T23:33:21.424Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-24T23:33:22.290Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-24T23:33:31.688Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-24_18-33-31_001.log
[2025-08-24T23:33:31.688Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-24T23:33:37.874Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-24T23:33:37.875Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-24T23:33:38.892Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-24T23:34:39.706Z] [INFO] [RealtimeLogger] Started logging dependency_trellis_stable_projectorz_101 to: dependency_python installation_2025-08-24_18-34-39_001.log
[2025-08-24T23:34:39.706Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Starting python installation for trellis-stable-projectorz-101
[2025-08-24T23:34:39.707Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Installing dependencies for trellis-stable-projectorz-101 (python:all)
[2025-08-24T23:34:39.708Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Component type: string, Component value: 'python'
[2025-08-24T23:34:39.708Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Name type: string, Name value: 'all'
[2025-08-24T23:34:39.709Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: About to check routing for trellis-stable-projectorz-101 with component python
