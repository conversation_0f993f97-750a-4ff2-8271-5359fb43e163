========================================
AIStudio Real-time Log: main
Started: 2025-08-24T23:15:25.534Z
File: app_main_2025-08-24_18-15-25_001.log
========================================

[2025-08-24T23:15:25.758Z] [INFO] AIStudio application started successfully
[2025-08-24T23:15:25.758Z] [INFO] [main] AIStudio application started successfully
[2025-08-24T23:15:25.793Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-24T23:15:26.698Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-24T23:15:36.539Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-24_18-15-36_001.log
[2025-08-24T23:15:36.539Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-24T23:15:38.929Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-24T23:15:38.930Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-24T23:15:39.946Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-24T23:16:31.770Z] [INFO] [RealtimeLogger] Started logging dependency_trellis_stable_projectorz_101 to: dependency_python installation_2025-08-24_18-16-31_001.log
[2025-08-24T23:16:31.770Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Starting python installation for trellis-stable-projectorz-101
[2025-08-24T23:16:31.772Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Installing dependencies for trellis-stable-projectorz-101 (python:all)
[2025-08-24T23:16:31.772Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Component type: string, Component value: 'python'
[2025-08-24T23:16:31.773Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Name type: string, Name value: 'all'
[2025-08-24T23:16:31.773Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: About to check routing for trellis-stable-projectorz-101 with component python
[2025-08-24T23:16:31.779Z] [ERROR] (node:39884) [DEP0147] DeprecationWarning: In future versions of Node.js, fs.rmdir(path, { recursive: true }) will be removed. Use fs.rm(path, { recursive: true }) instead
(Use `electron --trace-deprecation ...` to show where the warning was created)
