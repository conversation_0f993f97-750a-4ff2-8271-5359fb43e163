========================================
AIStudio Real-time Log: main
Started: 2025-08-24T23:18:56.442Z
File: app_main_2025-08-24_18-18-56_001.log
========================================

[2025-08-24T23:18:56.699Z] [INFO] AIStudio application started successfully
[2025-08-24T23:18:56.699Z] [INFO] [main] AIStudio application started successfully
[2025-08-24T23:18:56.738Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-24T23:18:57.662Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-24T23:19:04.754Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-24_18-19-04_001.log
[2025-08-24T23:19:04.755Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-24T23:19:07.175Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-24T23:19:07.176Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-24T23:19:08.179Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-24T23:22:19.950Z] [INFO] [RealtimeLogger] Started logging dependency_trellis_stable_projectorz_101 to: dependency_python installation_2025-08-24_18-22-19_001.log
[2025-08-24T23:22:19.950Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Starting python installation for trellis-stable-projectorz-101
[2025-08-24T23:22:19.951Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Installing dependencies for trellis-stable-projectorz-101 (python:all)
[2025-08-24T23:22:19.952Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Component type: string, Component value: 'python'
[2025-08-24T23:22:19.952Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Name type: string, Name value: 'all'
[2025-08-24T23:22:19.953Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: About to check routing for trellis-stable-projectorz-101 with component python
[2025-08-24T23:22:19.961Z] [ERROR] (node:21608) [DEP0147] DeprecationWarning: In future versions of Node.js, fs.rmdir(path, { recursive: true }) will be removed. Use fs.rm(path, { recursive: true }) instead
(Use `electron --trace-deprecation ...` to show where the warning was created)
